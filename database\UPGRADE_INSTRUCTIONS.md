# تعليمات ترقية نظام إدارة المقالات

## نظرة عامة
تم تحديث نظام إدارة المقالات ليشمل ميزات متقدمة وحقول جديدة لتحسين الأداء وسهولة الاستخدام.

## التحديثات المطبقة

### 1. إصلاح مشكلة اللوجو
- ✅ تم إصلاح مشكلة عدم ظهور اللوجو في صفحة تسجيل الدخول
- ✅ إضافة معالجة للأخطاء مع عرض أيقونة بديلة في حالة عدم وجود الصورة
- ✅ تحديث sidebar.php لعرض اللوجو بشكل صحيح

### 2. تحسين قاعدة البيانات
- ✅ إنشاء جدول مقالات محسن مع حقول جديدة
- ✅ إضافة فهارس محسنة للأداء
- ✅ دعم البحث النصي الكامل (Full-Text Search)

### 3. الحقول الجديدة المضافة
- `thumbnail` - صورة مصغرة للمقال
- `author_id` - معرف الكاتب
- `category_id` - معرف التصنيف
- `meta_keywords` - كلمات مفتاحية SEO
- `canonical_url` - الرابط الأساسي
- `sticky` - مقال مثبت
- `allow_comments` - السماح بالتعليقات
- `likes` - عدد الإعجابات
- `shares` - عدد المشاركات
- `reading_time` - وقت القراءة بالدقائق
- `word_count` - عدد الكلمات
- `language` - لغة المقال
- `published_at` - تاريخ النشر
- `scheduled_at` - تاريخ النشر المجدول
- `last_modified_by` - آخر من عدل المقال
- `version` - إصدار المقال
- `priority` - أولوية المقال
- `external_url` - رابط خارجي
- `source` - مصدر المقال
- `related_articles` - المقالات ذات الصلة
- `custom_fields` - حقول مخصصة (JSON)
- `seo_score` - نقاط SEO
- `readability_score` - نقاط سهولة القراءة

### 4. تحديث واجهة الإدارة
- ✅ إضافة حقول جديدة في نموذج إضافة المقال
- ✅ تحديث نموذج تعديل المقال
- ✅ تحسين عرض المقالات في الجدول
- ✅ إضافة معلومات إضافية مثل وقت القراءة والإعجابات

## خطوات التطبيق

### الخيار الأول: قاعدة بيانات جديدة
إذا كنت تريد إنشاء قاعدة بيانات جديدة:

```sql
-- تشغيل الملف الجديد
SOURCE database/articles_improved.sql;
```

### الخيار الثاني: تحديث قاعدة البيانات الموجودة
إذا كنت تريد تحديث قاعدة البيانات الموجودة:

```sql
-- تشغيل ملف التحديث
SOURCE database/update_articles_table.sql;
```

### الخيار الثالث: تحديث يدوي
يمكنك تشغيل الاستعلامات التالية يدوياً:

1. **إضافة الحقول الجديدة:**
```sql
ALTER TABLE `articles` 
ADD COLUMN `thumbnail` varchar(500) DEFAULT NULL COMMENT 'صورة مصغرة للمقال' AFTER `image`,
ADD COLUMN `author_id` int(11) DEFAULT NULL COMMENT 'معرف الكاتب' AFTER `author`,
-- ... باقي الحقول
```

2. **إضافة الفهارس:**
```sql
ALTER TABLE `articles` 
ADD INDEX `idx_sticky` (`sticky`),
ADD INDEX `idx_category_id` (`category_id`),
-- ... باقي الفهارس
```

3. **تحديث البيانات الموجودة:**
```sql
UPDATE `articles` SET 
`published_at` = `created_at` WHERE `status` = 'published' AND `published_at` IS NULL,
`language` = 'ar' WHERE `language` IS NULL;
```

## التحقق من نجاح التحديث

### 1. فحص بنية الجدول
```sql
DESCRIBE articles;
```

### 2. فحص الفهارس
```sql
SHOW INDEX FROM articles;
```

### 3. فحص البيانات
```sql
SELECT id, title, category, status, views, likes, reading_time, published_at 
FROM articles 
LIMIT 5;
```

## الميزات الجديدة

### 1. حساب تلقائي لوقت القراءة
- يتم حساب وقت القراءة تلقائياً بناءً على عدد الكلمات
- متوسط 200 كلمة في الدقيقة

### 2. نظام الإصدارات
- تتبع إصدارات المقال عند التعديل
- عرض رقم الإصدار في قائمة المقالات

### 3. نظام الأولوية
- إمكانية تحديد أولوية المقال (0-10)
- ترتيب المقالات حسب الأولوية

### 4. دعم المقالات المجدولة
- إمكانية جدولة نشر المقالات
- حالة جديدة "مجدول" للمقالات

### 5. تحسينات SEO
- حقول إضافية لتحسين محركات البحث
- نقاط SEO وسهولة القراءة

### 6. نظام التفاعل
- عدد الإعجابات والمشاركات
- إمكانية تفعيل/إلغاء التعليقات

## ملاحظات مهمة

### الأمان
- تم إضافة `htmlspecialchars()` لحماية من XSS
- تحسين استعلامات قاعدة البيانات

### الأداء
- إضافة فهارس مركبة لتحسين الأداء
- فهرس البحث النصي الكامل

### التوافق
- التحديث متوافق مع البيانات الموجودة
- لا يؤثر على المقالات المنشورة حالياً

## استكشاف الأخطاء

### مشكلة في إنشاء الفهارس
```sql
-- حذف الفهرس إذا كان موجوداً
DROP INDEX idx_search ON articles;
-- إعادة إنشاؤه
ALTER TABLE articles ADD FULLTEXT INDEX idx_search (title, excerpt, content, tags);
```

### مشكلة في الحقول الجديدة
```sql
-- التحقق من وجود الحقل
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'articles' AND COLUMN_NAME = 'reading_time';
```

### مشكلة في البيانات
```sql
-- إعادة حساب وقت القراءة
UPDATE articles SET 
word_count = (CHAR_LENGTH(content) - CHAR_LENGTH(REPLACE(content, ' ', '')) + 1),
reading_time = CEIL((CHAR_LENGTH(content) - CHAR_LENGTH(REPLACE(content, ' ', '')) + 1) / 200);
```

## الدعم
في حالة وجود أي مشاكل، يرجى:
1. التحقق من سجلات الأخطاء
2. فحص صلاحيات قاعدة البيانات
3. التأكد من إصدار MySQL/MariaDB

---
**تاريخ التحديث:** 2025-07-24  
**الإصدار:** 2.0  
**المطور:** فريق نقرة للتسويق الإلكتروني
